import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:venvi/models/participant_model.dart';

part 'role_manager_state.freezed.dart';

@freezed
sealed class RoleManagerState with _$RoleManagerState {
  const factory RoleManagerState({
    String? roleName,
    required List<ParticipantModel> spotlightParticipants,
    required List<ParticipantModel> regularParticipants,
    required Map<String, ParticipantModel> originalParticipantsById,
    required Map<String, ParticipantModel> originalParticipantsByUserId,
    required Set<String> currentParticipantIds,
    required Set<String> currentUserIds,
  }) = _RoleManagerState;
}
