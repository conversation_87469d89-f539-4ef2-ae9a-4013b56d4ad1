import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:venvi/constants/rules.dart';
import 'package:venvi/global_view_models/participant_view_model.dart';
import 'package:venvi/models/participant_model.dart';
import 'package:venvi/repositories/auth_repository.dart';
import 'package:venvi/router.dart';
import 'package:venvi/screen_dialogs/edit_profile/edit_manual_participant_screen.dart';
import 'package:venvi/screen_dialogs/edit_profile/edit_profile_screen.dart';
import 'package:venvi/widgets/content_area.dart';
import 'package:venvi/widgets/glass_app_bar.dart';
import 'package:venvi/widgets/profile_page.dart';
import 'package:venvi/widgets/restricted_view.dart';

class ParticipantDetailsScreen extends StatelessWidget {
  final String? participantId;

  const ParticipantDetailsScreen({super.key, required this.participantId});

  @override
  Widget build(BuildContext context) {
    final participantId = this.participantId;
    if (participantId == null) {
      return Scaffold(
        appBar: GlassAppBar(),
        body: ContentArea(fillWidth: false, child: Text('No participant ID provided')),
      );
    }

    late final ParticipantModel? participantModel;
    try {
      participantModel = context.read<ParticipantViewModel>().getParticipant(participantId);
    } catch (e) {
      participantModel = null;
    }

    if (participantModel == null) {
      return Scaffold(
        appBar: GlassAppBar(),
        body: ContentArea(fillWidth: false, child: Text('Participant not found')),
      );
    }

    return Scaffold(
      appBar: GlassAppBar(actions: _buildAppBarActions(context, participantModel)),
      body: ProfilePage(participantModel: participantModel),
    );
  }

  List<Widget>? _buildAppBarActions(BuildContext context, ParticipantModel participantModel) {
    return [
      if (participantModel.userId == null)
        RestrictedView(
          rule: Rules.editParticipantsAndRoles,
          child: IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () => AppRouter.pushFullScreenDialog<ParticipantModel?>(
              context,
              EditManualParticipantScreen(participant: participantModel),
            ),
          ),
        )
      else if (context.watch<AuthRepository>().currentUser?.uid == participantModel.userId)
        IconButton(
          icon: const Icon(Icons.edit),
          onPressed: () => AppRouter.pushFullScreenDialog(context, const EditProfileScreen()),
        ),
    ];
  }
}
