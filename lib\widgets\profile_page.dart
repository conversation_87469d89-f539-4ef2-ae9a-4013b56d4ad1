import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:venvi/global_view_models/con_view_model.dart';
import 'package:venvi/global_view_models/event_view_model.dart';
import 'package:venvi/global_view_models/role_view_model.dart';
import 'package:venvi/models/event_model.dart';
import 'package:venvi/models/participant_model.dart';
import 'package:venvi/models/profile_model.dart';
import 'package:venvi/models/role_model.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/utils/url_handler.dart';
import 'package:venvi/widgets/accent_label.dart';
import 'package:venvi/widgets/agenda_group.dart';
import 'package:venvi/widgets/content_area.dart';
import 'package:venvi/widgets/profile_photo_view.dart';

class ProfilePage extends StatelessWidget {
  final ParticipantModel? participantModel;
  final ProfileModel? profileModel;

  const ProfilePage({super.key, this.participantModel, this.profileModel})
    : assert(
        (participantModel != null) != (profileModel != null),
        'Must provide either participantModel or profileModel but not both',
      );

  @override
  Widget build(BuildContext context) {
    final participantModel = this.participantModel;
    final profileModel = this.profileModel;
    final profileBase = participantModel ?? profileModel;
    if (profileBase == null || (participantModel != null) != (profileModel != null)) {
      return Center(child: ContentArea(fillWidth: false, child: Text('Profile not found')));
    }

    final headline = participantModel?.conHeadline ?? participantModel?.headline ?? profileModel?.headline;
    final bio = participantModel?.conBio ?? participantModel?.bio ?? profileModel?.bio;
    final socialMediaLinks = participantModel?.socialMediaLinks ?? profileModel?.socialMediaLinks;

    return ListView(
      padding: const EdgeInsets.all(AppTheme.screenPadding),
      children: [
        ProfilePhotoView(profile: profileBase, size: 200),
        const SizedBox(height: AppTheme.widgetPaddingLarge),
        ContentArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SelectableText(profileBase.displayName ?? '', style: Theme.of(context).textTheme.titleLarge),
              const SizedBox(height: AppTheme.widgetPaddingVerySmall),
              SelectableText(
                profileBase.username ?? '',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(
                    context,
                  ).colorScheme.onPrimaryContainer.withValues(alpha: AppTheme.subtleWidgetOpacity),
                ),
              ),
              if (headline != null)
                Padding(
                  padding: const EdgeInsets.only(top: AppTheme.widgetPaddingSmall),
                  child: SelectableText(headline, style: Theme.of(context).textTheme.bodyMedium),
                ),
              if (participantModel != null && participantModel.allRoles != null)
                BlocBuilder<RoleViewModel, List<RoleModel>?>(
                  builder: (context, state) => Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      for (final role in state ?? List<RoleModel>.empty())
                        participantModel.allRoles!.contains(role.id)
                            ? Padding(
                                padding: const EdgeInsets.only(top: AppTheme.widgetPaddingSmall),
                                child: AccentLabel(
                                  text: role.name ?? '',
                                  icon: participantModel.spotlightRoles?.contains(role.id) == true ? Icons.star : null,
                                  isOnContainer: true,
                                ),
                              )
                            : const SizedBox.shrink(),
                    ],
                  ),
                ),
            ],
          ),
        ),
        if (socialMediaLinks != null && socialMediaLinks.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(top: AppTheme.widgetPadding),
            child: ContentArea(
              child: Wrap(
                alignment: WrapAlignment.center,
                spacing: AppTheme.widgetPadding,
                children: [
                  for (final socialMedia in socialMediaLinks.keys)
                    IconButton(
                      tooltip: socialMedia.text,
                      icon: Icon(socialMedia.icon),
                      iconSize: 32,
                      onPressed: () => UrlHandler.open('${socialMedia.url}${socialMediaLinks[socialMedia]}'),
                    ),
                ],
              ),
            ),
          ),
        if (bio != null)
          Padding(
            padding: const EdgeInsets.only(top: AppTheme.widgetPadding),
            child: ContentArea(child: SelectableText(bio)),
          ),
        if (participantModel != null) _buildParticipantEventsList(context, participantModel),
      ],
    );
  }

  Widget _buildParticipantEventsList(BuildContext context, ParticipantModel participantModel) {
    return BlocBuilder<EventViewModel, List<EventModel>?>(
      builder: (context, state) {
        final participantEvents = state
            ?.where((event) => event.participantIds?.contains(participantModel.id) == true)
            .toList();
        if (participantEvents == null || participantEvents.isEmpty) {
          return const SizedBox.shrink();
        }

        final groupedEvents = context.read<EventViewModel>().groupEventsByDateAndTime(
          context.watch<ConViewModel>().state,
          participantEvents,
        );

        List<Widget> agendaGroups = [];
        for (final day in groupedEvents.keys) {
          final List<EventModel> dayEvents = [];
          groupedEvents[day]!.forEach((time, events) {
            dayEvents.addAll(events);
          });

          agendaGroups.add(AgendaGroup(title: _formatDay(day.toDateTime()), events: dayEvents));
        }
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const SizedBox(height: AppTheme.widgetPaddingLarge),
            AccentLabel(
              text:
                  'Events with ${participantModel.displayName != null ? participantModel.displayName! : 'Participant'}',
              isOnContainer: false,
            ),
            for (final agendaGroup in agendaGroups)
              Padding(
                padding: const EdgeInsets.only(top: AppTheme.widgetPadding),
                child: agendaGroup,
              ),
          ],
        );
      },
    );
  }

  String _formatDay(DateTime day) {
    return DateFormat.MMMEd().format(day);
  }
}
