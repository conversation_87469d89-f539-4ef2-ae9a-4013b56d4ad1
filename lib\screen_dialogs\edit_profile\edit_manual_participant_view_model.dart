import 'package:image_picker/image_picker.dart';
import 'package:venvi/constants/image_type.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/models/image_model.dart';
import 'package:venvi/models/participant_model.dart';
import 'package:venvi/repositories/image_repository.dart';
import 'package:venvi/repositories/participant_repository.dart';
import 'package:venvi/screen_dialogs/edit_profile/edit_manual_participant_state.dart';
import 'package:venvi/widgets/editor_scaffold_exception.dart';
import 'package:venvi/widgets/editor_scaffold_view_model.dart';

class EditManualParticipantViewModel extends EditorScaffoldViewModel<EditManualParticipantState> {
  final ConData conData;
  final ParticipantRepository _participantRepository;
  final ImageRepository _imageRepository;
  final bool _isNewParticipant;

  EditManualParticipantViewModel(
    ParticipantModel initialParticipant,
    this.conData,
    this._participantRepository,
    this._imageRepository,
    this._isNewParticipant,
  ) : super(EditManualParticipantState(participantModel: initialParticipant));

  @override
  bool checkChanges(EditManualParticipantState initialState, EditManualParticipantState currentState) {
    return currentState.participantModel != initialState.participantModel ||
        currentState.participantPhotoOverrideFile != null;
  }

  @override
  Future<EditManualParticipantState> applyChanges(
    EditManualParticipantState initialState,
    EditManualParticipantState state,
  ) async {
    final errors = <String>[];

    if (state.participantModel.displayName?.isNotEmpty != true) {
      errors.add('Display name is required');
    }

    if (errors.isNotEmpty) {
      throw EditorScaffoldException(errors);
    }

    ParticipantModel model = state.participantModel;

    // Creates image model for participant photo
    late final ImageModel? participantPhotoUploadModel;
    if (state.participantPhotoOverrideFile != null) {
      participantPhotoUploadModel = _imageRepository.createImageModel(conData, ImageType.participantPhoto);
      model = model.copyWith(
        participantPhoto: participantPhotoUploadModel,
        photoUrl: participantPhotoUploadModel.downloadUrl,
      );
    } else {
      participantPhotoUploadModel = null;
      final photoUrl = model.participantPhoto?.downloadUrl ?? model.profilePhotoUrl;
      model = model.copyWith(photoUrl: photoUrl);
    }

    if (_isNewParticipant) {
      final docId = await _participantRepository.createParticipant(conData, model);
      if (docId != null) {
        model = model.copyWith(id: docId);
        // Upload profile photo after participant is created
        if (participantPhotoUploadModel != null && state.participantPhotoOverrideFile != null) {
          await _imageRepository.uploadImage(participantPhotoUploadModel, state.participantPhotoOverrideFile!);
        }
        return state.copyWith(participantModel: model);
      } else {
        throw const EditorScaffoldException(['Failed to create profile']);
      }
    } else {
      final participantId = model.id;
      if (participantId == null) {
        throw const EditorScaffoldException(['Failed to update profile']);
      }
      final success = await _participantRepository.updateParticipant(conData, participantId, model);
      if (success) {
        // Upload profile photo after participant is updated
        if (participantPhotoUploadModel != null && state.participantPhotoOverrideFile != null) {
          await _imageRepository.uploadImage(participantPhotoUploadModel, state.participantPhotoOverrideFile!);
        }
        return state.copyWith(participantModel: model);
      } else {
        throw const EditorScaffoldException(['Failed to update profile']);
      }
    }
  }

  void setDisplayName(String displayName) {
    emit(state.copyWith(participantModel: state.participantModel.copyWith(displayName: displayName.trim())));
  }

  void setConHeadline(String conHeadline) {
    emit(state.copyWith(participantModel: state.participantModel.copyWith(conHeadline: conHeadline.trim())));
  }

  void setConBio(String conBio) {
    emit(state.copyWith(participantModel: state.participantModel.copyWith(conBio: conBio.trim())));
  }

  void setParticipantPhotoFile(XFile image) {
    final newState = state.copyWith(
      participantModel: state.participantModel.copyWith(participantPhoto: null),
      participantPhotoOverrideFile: image,
    );
    emit(newState);
  }

  void removeParticipantPhoto() {
    final updatedModel = state.participantModel.copyWith(
      participantPhoto: null,
      photoUrl: state.participantModel.profilePhotoUrl,
    );
    final newState = state.copyWith(participantModel: updatedModel, participantPhotoOverrideFile: null);
    emit(newState);
  }
}
