import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:venvi/constants/permissions.dart';
import 'package:venvi/global_view_models/participant_view_model.dart';
import 'package:venvi/models/participant_model.dart';
import 'package:venvi/models/profile_model.dart';
import 'package:venvi/models/profile_snippet_model.dart';
import 'package:venvi/repositories/permission_repository.dart';
import 'package:venvi/repositories/profile_repository.dart';

class PermissionsManagerViewModel extends Cubit<Map<Permissions, List<ProfileSnippetModel>>> {
  final String _orgId;
  final PermissionRepository _permissionRepository;

  late final StreamSubscription? _permissionsSubscription;

  PermissionsManagerViewModel(
    this._orgId,
    this._permissionRepository,
    ParticipantViewModel participantViewModel,
    ProfileRepository profileRepository,
  ) : super(const {}) {
    _permissionsSubscription = _permissionRepository.getOrgPermissionsStream(_orgId).listen((permissionModels) async {
      if (permissionModels == null) {
        emit(const {});
        return;
      }

      final permissionProfiles = <Permissions, List<ProfileSnippetModel>>{};
      for (final permissionModel in permissionModels) {
        final userId = permissionModel.id;
        final permissions = permissionModel.permissions;
        if (userId == null || permissions == null || permissions.isEmpty) {
          continue;
        }
        late final ProfileSnippetModel? profileSnippet;
        final participantModel = participantViewModel.getParticipantByUserId(userId);
        if (participantModel != null) {
          profileSnippet = _convertFromParticipantModel(participantModel);
        } else {
          final profileModel = await profileRepository.getProfile(userId);
          if (profileModel != null) {
            profileSnippet = _convertFromProfileModel(profileModel);
          } else {
            continue;
          }
        }

        if (profileSnippet == null) {
          continue;
        }

        // For each permission user has, add to map
        for (final permission in permissions) {
          if (permissionProfiles.containsKey(permission)) {
            permissionProfiles[permission]!.add(profileSnippet);
          } else {
            permissionProfiles[permission] = [profileSnippet];
          }
        }
      }

      emit(permissionProfiles);
    });
  }

  @override
  Future<void> close() async {
    _permissionsSubscription?.cancel();
    return super.close();
  }

  Future<bool> addPermission(String userId, Permissions permission) async {
    return _permissionRepository.addPermission(_orgId, userId, permission);
  }

  Future<bool> removePermission(String userId, Permissions permission) async {
    return _permissionRepository.removePermission(_orgId, userId, permission);
  }

  Future<bool> changeOrgOwner(String oldOwnerId, String newOwnerId) async {
    return _permissionRepository.changeOrgOwner(_orgId, oldOwnerId, newOwnerId);
  }

  ProfileSnippetModel? _convertFromParticipantModel(ParticipantModel participantModel) {
    if (participantModel.userId == null) {
      return null;
    }
    return ProfileSnippetModel(
      id: participantModel.userId,
      username: participantModel.username,
      displayName: participantModel.displayName,
      photoUrl: participantModel.photoUrl,
      headline: participantModel.conHeadline ?? participantModel.headline,
    );
  }

  ProfileSnippetModel? _convertFromProfileModel(ProfileModel profileModel) {
    return ProfileSnippetModel(
      id: profileModel.id,
      username: profileModel.username,
      displayName: profileModel.displayName,
      photoUrl: profileModel.photoUrl,
      headline: profileModel.headline,
    );
  }
}
