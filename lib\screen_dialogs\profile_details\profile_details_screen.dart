import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:venvi/constants/rules.dart';
import 'package:venvi/models/base_profile.dart';
import 'package:venvi/models/participant_model.dart';
import 'package:venvi/models/profile_model.dart';
import 'package:venvi/repositories/auth_repository.dart';
import 'package:venvi/router.dart';
import 'package:venvi/screen_dialogs/edit_profile/edit_manual_participant_screen.dart';
import 'package:venvi/screen_dialogs/edit_profile/edit_profile_screen.dart';
import 'package:venvi/widgets/glass_app_bar.dart';
import 'package:venvi/widgets/profile_page.dart';
import 'package:venvi/widgets/restricted_view.dart';

class ProfileDetailsScreen extends StatelessWidget {
  final BaseProfile? profile;

  const ProfileDetailsScreen({super.key, required this.profile});

  @override
  Widget build(BuildContext context) {
    if (profile == null) {
      return Scaffold(
        appBar: GlassAppBar(isFullScreenDialog: true),
        body: const Center(child: Text('Profile not found')),
      );
    }

    final participantModel = profile is ParticipantModel ? profile as ParticipantModel : null;
    final profileModel = participantModel == null && profile is ProfileModel ? profile as ProfileModel : null;

    return Scaffold(
      appBar: GlassAppBar(
        isFullScreenDialog: true,
        actions: _buildAppBarActions(context, participantModel, profileModel),
      ),
      body: ProfilePage(participantModel: participantModel, profileModel: profileModel),
    );
  }

  List<Widget>? _buildAppBarActions(
    BuildContext context,
    ParticipantModel? participantModel,
    ProfileModel? profileModel,
  ) {
    final currentUserId = context.watch<AuthRepository>().currentUser?.uid;
    return [
      if (participantModel != null && participantModel.userId == null)
        RestrictedView(
          rule: Rules.editParticipantsAndRoles,
          child: IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () => AppRouter.pushFullScreenDialog<ParticipantModel?>(
              context,
              EditManualParticipantScreen(participant: participantModel),
            ),
          ),
        )
      else if (currentUserId != null && currentUserId == (participantModel?.userId ?? profileModel?.id))
        IconButton(
          icon: const Icon(Icons.edit),
          onPressed: () => AppRouter.pushFullScreenDialog(context, const EditProfileScreen()),
        ),
    ];
  }
}
