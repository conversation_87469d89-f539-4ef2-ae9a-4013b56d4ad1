import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:venvi/constants/data_location.dart';
import 'package:venvi/constants/permissions.dart';
import 'package:venvi/models/permission_model.dart';
import 'package:venvi/utils/logger.dart';

class PermissionRepository {
  final FirebaseFirestore _firestore;

  PermissionRepository(this._firestore);

  Stream<List<PermissionModel>?> getOrgPermissionsStream(String orgId) {
    final ref = _firestore.collection('orgs').doc(orgId).collection('permissions');
    try {
      return ref.snapshots().map((event) {
        return event.docs
            .map((doc) {
              try {
                return PermissionModel.fromJson(doc.data());
              } catch (e, stackTrace) {
                Logger.error(exception: e, stackTrace: stackTrace);
                return null;
              }
            })
            .whereType<PermissionModel>()
            .toList();
      });
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to get doc stream',
        collectionReference: ref,
      );
      return Stream.value(null);
    }
  }

  Future<PermissionModel?> getUserPermissions(
    String orgId,
    String userId, {
    DataLocation? dataLocation,
    Duration? timeout,
  }) async {
    final ref = _firestore.collection('orgs').doc(orgId).collection('permissions').doc(userId);
    try {
      final snapshotFuture = ref.get(GetOptions(source: dataLocation?.firestoreSource ?? Source.serverAndCache));
      final snapshot = timeout != null ? await snapshotFuture.timeout(timeout) : await snapshotFuture;

      if (snapshot.exists) {
        final data = snapshot.data();
        if (data != null) {
          return PermissionModel.fromJson(data);
        }
      }
      return null;
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to get doc',
        documentReference: ref,
        dataLocation: dataLocation,
      );
      return null;
    }
  }

  Future<bool> addPermission(String orgId, String userId, Permissions role) async {
    final ref = _firestore.collection('orgs').doc(orgId).collection('permissions').doc(userId);
    try {
      await ref.set({
        'orgId': orgId,
        'permissions': FieldValue.arrayUnion([role.name]),
      }, SetOptions(merge: true));
      return true;
    } catch (e, stackTrace) {
      Logger.firestoreError(exception: e, stackTrace: stackTrace, message: 'Failed to set doc', documentReference: ref);
      return false;
    }
  }

  Future<bool> removePermission(String orgId, String userId, Permissions role) async {
    final ref = _firestore.collection('orgs').doc(orgId).collection('permissions').doc(userId);
    try {
      await ref.set({
        'orgId': orgId,
        'permissions': FieldValue.arrayRemove([role.name]),
      }, SetOptions(merge: true));
      return true;
    } catch (e, stackTrace) {
      Logger.firestoreError(exception: e, stackTrace: stackTrace, message: 'Failed to set doc', documentReference: ref);
      return false;
    }
  }

  Future<bool> changeOrgOwner(String orgId, String oldOwnerId, String newOwnerId) async {
    final addedAdminSuccess = await addPermission(orgId, oldOwnerId, Permissions.orgAdmin);
    if (!addedAdminSuccess) {
      return false;
    }

    final oldOwnerRef = _firestore.collection('orgs').doc(orgId).collection('permissions').doc(oldOwnerId);
    final newOwnerRef = _firestore.collection('orgs').doc(orgId).collection('permissions').doc(newOwnerId);
    try {
      final batch = _firestore.batch();
      batch.set(oldOwnerRef, {
        'orgId': orgId,
        'permissions': FieldValue.arrayRemove([Permissions.orgOwner.name]),
      }, SetOptions(merge: true));
      batch.set(newOwnerRef, {
        'orgId': orgId,
        'permissions': FieldValue.arrayUnion([Permissions.orgOwner.name]),
      }, SetOptions(merge: true));
      await batch.commit();
      return true;
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to set doc',
        documentReference: oldOwnerRef,
      );
      return false;
    }
  }
}
