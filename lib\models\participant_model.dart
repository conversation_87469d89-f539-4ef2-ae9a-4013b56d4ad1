import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:venvi/constants/social_media.dart';
import 'package:venvi/models/base_profile.dart';
import 'package:venvi/models/image_model.dart';
import 'package:venvi/models/profile_model.dart';

part 'participant_model.freezed.dart';
part 'participant_model.g.dart';

@freezed
sealed class ParticipantModel with _$ParticipantModel implements BaseProfile {
  const factory ParticipantModel({
    String? id,
    String? userId,
    String? username,
    String? displayName,
    String? searchUsername,
    String? searchDisplayName,
    String? profilePhotoUrl,
    ImageModel? participantPhoto,
    String? headline,
    String? bio,
    Map<SocialMedia, String>? socialMediaLinks,
    String? conHeadline,
    String? conBio,
    List<String>? allRoles,
    List<String>? spotlightRoles,
  }) = _ParticipantModel;

  factory ParticipantModel.fromProfileModel(ProfileModel profileModel) {
    final participantModel = ParticipantModel.fromJson(profileModel.toJson());
    return participantModel.copyWith(id: null, userId: profileModel.id);
  }

  factory ParticipantModel.fromJson(Map<String, dynamic> json) => _$ParticipantModelFromJson(json);
}

extension ParticipantModelExtension on ParticipantModel {
  String? get photoUrl => participantPhoto?.downloadUrl ?? profilePhotoUrl;
}
