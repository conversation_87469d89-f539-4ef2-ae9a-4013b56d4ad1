import 'dart:async';

import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/global_view_models/participant_view_model.dart';
import 'package:venvi/models/participant_model.dart';
import 'package:venvi/models/profile_model.dart';
import 'package:venvi/repositories/participant_repository.dart';
import 'package:venvi/repositories/role_repository.dart';
import 'package:venvi/screens/roles_and_participants/role_manager_state.dart';
import 'package:venvi/widgets/editor_scaffold_exception.dart';
import 'package:venvi/widgets/editor_scaffold_view_model.dart';

class RoleManagerViewModel extends EditorScaffoldViewModel<RoleManagerState> {
  final String _roleId;
  final ConData _conData;
  final RoleRepository _roleRepository;
  final ParticipantViewModel _participantViewModel;
  final ParticipantRepository _participantRepository;

  RoleManagerViewModel(
    super.initialState,
    this._roleId,
    this._conData,
    this._roleRepository,
    this._participantViewModel,
    this._participantRepository,
  );

  @override
  bool checkChanges(RoleManagerState initialState, RoleManagerState currentState) {
    return initialState.roleName != currentState.roleName ||
        initialState.regularParticipants.length != currentState.regularParticipants.length ||
        initialState.spotlightParticipants.length != currentState.spotlightParticipants.length ||
        !initialState.regularParticipants.every((element) => currentState.regularParticipants.contains(element)) ||
        !initialState.spotlightParticipants.every((element) => currentState.spotlightParticipants.contains(element));
  }

  @override
  Future<RoleManagerState> applyChanges(RoleManagerState initialState, RoleManagerState state) async {
    // Participants changed
    Future<bool>? participantFuture;
    if (initialState.regularParticipants.length != state.regularParticipants.length ||
        initialState.spotlightParticipants.length != state.spotlightParticipants.length ||
        !initialState.regularParticipants.every((element) => state.regularParticipants.contains(element)) ||
        !initialState.spotlightParticipants.every((element) => state.spotlightParticipants.contains(element))) {
      final initialAllParticipants = initialState.regularParticipants + initialState.spotlightParticipants;
      final allParticipants = state.regularParticipants + state.spotlightParticipants;

      final conParticipantIds = _participantViewModel.state?.map((e) => e.id).whereType<String>().toList() ?? [];
      final newParticipants = allParticipants.where((element) => !conParticipantIds.contains(element.id)).toList();

      // Create new participants
      final List<Future<String?>> participantCreationFutures = [];
      for (final participant in newParticipants) {
        participantCreationFutures.add(_participantRepository.createParticipant(_conData, participant));
      }
      final result = await Future.wait(participantCreationFutures);
      final success = result.every((element) => element != null);

      if (!success) {
        throw const EditorScaffoldException(['Failed to create participants']);
      }

      final removedParticipantIds = initialAllParticipants
          .where((element) => !allParticipants.contains(element))
          .map((e) => e.id)
          .whereType<String>()
          .toList();
      final spotlightParticipantIds = state.spotlightParticipants.map((e) => e.id).whereType<String>().toList();
      final allParticipantIds = allParticipants.map((e) => e.id).whereType<String>().toList();

      participantFuture = _participantRepository.updateRole(
        _conData,
        _roleId,
        allParticipantIds,
        spotlightParticipantIds,
        removedParticipantIds,
      );
    }

    // Role changed
    Future<bool>? roleFuture;
    final newName = state.roleName?.trim() ?? '';
    if (initialState.roleName != newName) {
      roleFuture = _roleRepository.updateName(_conData, _roleId, newName);
    }

    final List<String> errors = [];
    if (await participantFuture == false) {
      errors.add('Failed to update participants');
    }
    if (await roleFuture == false) {
      errors.add('Failed to update title');
    }
    if (errors.isNotEmpty) {
      throw EditorScaffoldException(errors);
    }

    return state;
  }

  void updateRoleName(String name) {
    emit(state.copyWith(roleName: name.trim()));
  }

  void addParticipantFromProfile(ProfileModel model) {
    final userId = model.id;
    if (userId == null) {
      return;
    }

    if (state.currentUserIds.contains(userId)) {
      return;
    }

    // Check if user previously existed
    final originalParticipant = state.originalParticipantsByUserId[userId];
    if (originalParticipant != null) {
      _addExistingParticipant(originalParticipant);
    } else {
      final newParticipant = ParticipantModel.fromProfileModel(model);
      _addNewParticipant(newParticipant);
    }
  }

  void addParticipant(ParticipantModel model) {
    final userId = model.userId;
    final participantId = model.id;

    // Check for duplicates
    if (userId != null && state.currentUserIds.contains(userId)) {
      return;
    }
    if (participantId != null && state.currentParticipantIds.contains(participantId)) {
      return;
    }

    // Check if user previously existed
    if (userId != null) {
      final originalParticipant = state.originalParticipantsByUserId[userId];
      if (originalParticipant != null) {
        _addExistingParticipant(originalParticipant);
        return;
      }
    }

    // Check if participant previously existed
    if (participantId != null) {
      final originalParticipant = state.originalParticipantsById[participantId];
      if (originalParticipant != null) {
        _addExistingParticipant(originalParticipant);
        return;
      }
    }

    _addNewParticipant(model);
  }

  void _addExistingParticipant(ParticipantModel participant) {
    final updatedUserIds = Set<String>.from(state.currentUserIds);
    final updatedParticipantIds = Set<String>.from(state.currentParticipantIds);

    if (participant.userId != null) {
      updatedUserIds.add(participant.userId!);
    }
    if (participant.id != null) {
      updatedParticipantIds.add(participant.id!);
    }

    emit(
      state.copyWith(
        regularParticipants: [...state.regularParticipants, participant],
        currentUserIds: updatedUserIds,
        currentParticipantIds: updatedParticipantIds,
      ),
    );
  }

  void _addNewParticipant(ParticipantModel participant) {
    final updatedUserIds = Set<String>.from(state.currentUserIds);
    final updatedParticipantIds = Set<String>.from(state.currentParticipantIds);

    if (participant.userId != null) {
      updatedUserIds.add(participant.userId!);
    }
    if (participant.id != null) {
      updatedParticipantIds.add(participant.id!);
    }

    emit(
      state.copyWith(
        regularParticipants: [...state.regularParticipants, participant],
        currentUserIds: updatedUserIds,
        currentParticipantIds: updatedParticipantIds,
      ),
    );
  }

  void removeParticipant(String participantId) {
    ParticipantModel? removedParticipant;
    try {
      removedParticipant = state.regularParticipants.firstWhere((element) => element.id == participantId);
    } catch (_) {
      try {
        removedParticipant = state.spotlightParticipants.firstWhere((element) => element.id == participantId);
      } catch (_) {
        return;
      }
    }

    // Update tracking sets
    final updatedUserIds = Set<String>.from(state.currentUserIds);
    final updatedParticipantIds = Set<String>.from(state.currentParticipantIds);

    if (removedParticipant.userId != null) {
      updatedUserIds.remove(removedParticipant.userId!);
    }
    updatedParticipantIds.remove(participantId);

    emit(
      state.copyWith(
        regularParticipants: state.regularParticipants.where((element) => element.id != participantId).toList(),
        spotlightParticipants: state.spotlightParticipants.where((element) => element.id != participantId).toList(),
        currentUserIds: updatedUserIds,
        currentParticipantIds: updatedParticipantIds,
      ),
    );
  }

  void setParticipantSpotlight(String participantId, bool spotlight) {
    ParticipantModel? participant;
    try {
      participant = state.regularParticipants.firstWhere((element) => element.id == participantId);
    } catch (_) {
      try {
        participant = state.spotlightParticipants.firstWhere((element) => element.id == participantId);
      } catch (_) {
        return;
      }
    }
    final spotlightParticipants = state.spotlightParticipants.toList();
    final regularParticipants = state.regularParticipants.toList();
    if (spotlight) {
      regularParticipants.remove(participant);
      spotlightParticipants.add(participant);
    } else {
      spotlightParticipants.remove(participant);
      regularParticipants.add(participant);
    }
    emit(state.copyWith(spotlightParticipants: spotlightParticipants, regularParticipants: regularParticipants));
  }

  Future<bool> deleteRole(ConData conData, RoleRepository roleRepository) async {
    return await roleRepository.deleteRole(conData, _roleId);
  }
}
