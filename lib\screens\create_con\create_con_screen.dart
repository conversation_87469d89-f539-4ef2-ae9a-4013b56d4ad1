import 'dart:io';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flex_color_picker/flex_color_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:sign_in_button/sign_in_button.dart';
import 'package:venvi/constants/input_constants.dart';
import 'package:venvi/constants/permissions.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/custom_classes/date_fields.dart';
import 'package:venvi/custom_classes/user_friendly_exception.dart';
import 'package:venvi/global_view_models/auth_view_model.dart';
import 'package:venvi/json_converters/color_converter.dart';
import 'package:venvi/models/location_model.dart';
import 'package:venvi/repositories/auth_repository.dart';
import 'package:venvi/repositories/create_con_repository.dart';
import 'package:venvi/repositories/financial_repository.dart';
import 'package:venvi/repositories/profile_repository.dart';
import 'package:venvi/router.dart';
import 'package:venvi/screen_dialogs/location_search/location_search_screen.dart';
import 'package:venvi/screen_dialogs/profile_details/profile_details_screen.dart';
import 'package:venvi/screens/admin_tools/demo_app.dart';
import 'package:venvi/screens/create_con/create_con_state.dart';
import 'package:venvi/screens/create_con/create_con_view_model.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/utils/bottom_sheets.dart';
import 'package:venvi/utils/dialogs.dart';
import 'package:venvi/utils/url_handler.dart';
import 'package:venvi/widgets/content_area.dart';
import 'package:venvi/widgets/content_group.dart';
import 'package:venvi/widgets/glass_app_bar.dart';
import 'package:venvi/widgets/glass_tile_button.dart';
import 'package:venvi/widgets/gradient_background.dart';
import 'package:venvi/widgets/input_error_message.dart';
import 'package:venvi/widgets/profile_item.dart';
import 'package:venvi/widgets/surface_input_field.dart';
import 'package:venvi/widgets/tier_selection.dart';

class CreateConScreen extends HookWidget {
  final String? orgId;

  const CreateConScreen({super.key, required this.orgId});

  @override
  Widget build(BuildContext context) {
    final colorHexController = useTextEditingController();

    return BlocProvider(
      create: (context) => CreateConViewModel(
        orgId,
        context.read<CreateConRepository>(),
        context.read<AuthRepository>(),
        context.read<ProfileRepository>(),
        context.read<FinancialRepository>(),
      ),
      child: BlocBuilder<CreateConViewModel, CreateConState>(
        builder: (context, state) {
          final viewModel = context.read<CreateConViewModel>();
          final colorHex = state.color;
          return Theme(
            data: AppTheme.createThemeData(colorHex, Theme.of(context).brightness),
            child: GradientBackground(
              color: colorHex != null ? ColorConverter.stringToColor(colorHex) : null,
              child: Scaffold(
                appBar: GlassAppBar(title: Text('Create a Con')),
                body: state.showTierSelector != null
                    ? ListView(
                        padding: const EdgeInsets.all(AppTheme.screenPadding),
                        children: [
                          if (orgId == null)
                            Padding(
                              padding: const EdgeInsets.only(bottom: AppTheme.widgetPadding),
                              child: ContentArea(
                                padding: EdgeInsets.zero,
                                child: GlassTileButton(
                                  icon: Icons.co_present,
                                  text: 'Schedule a demo with our team',
                                  trailingIcon: Icons.open_in_new,
                                  isAccent: true,
                                  onTap: () => UrlHandler.open('https://venvi.app'),
                                ),
                              ),
                            ),
                          if (orgId == null)
                            Padding(
                              padding: EdgeInsets.only(bottom: AppTheme.widgetPadding),
                              child: ContentArea(
                                child: Column(
                                  children: [
                                    Text(
                                      'Creating New Organization',
                                      textAlign: TextAlign.center,
                                      style: Theme.of(context).textTheme.headlineSmall,
                                    ),
                                    const SizedBox(height: AppTheme.widgetPaddingSmall),
                                    Text(
                                      'If you want to create a con under an existing organization,\nOpen the con > Admin tab > Organization section > Manage Organization\n\nNote: Only Org Owner can create new cons',
                                      textAlign: TextAlign.center,
                                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                        color: Theme.of(context).colorScheme.onPrimaryContainer.withValues(
                                          alpha: AppTheme.subtleWidgetOpacity,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          if (orgId == null)
                            Padding(
                              padding: const EdgeInsets.only(bottom: AppTheme.widgetPadding),
                              child: ContentGroup(
                                title: Permissions.orgOwner.text,
                                subtitle: Permissions.orgOwner.description,
                                padHorizontally: false,
                                child: BlocBuilder<AuthViewModel, User?>(
                                  builder: (context, authState) => authState == null || authState.isAnonymous
                                      ? Column(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            SignInButton(
                                              Buttons.google,
                                              elevation: 0,
                                              onPressed: () async {
                                                try {
                                                  final user = await context.read<AuthRepository>().signInWithGoogle();
                                                  viewModel.setOrgOwner(user.uid);
                                                } catch (e) {
                                                  if (!context.mounted) {
                                                    return;
                                                  }
                                                  if (e is UserFriendlyException) {
                                                    if (e.message != null) {
                                                      Dialogs.showErrorDialog(context, message: e.message);
                                                    }
                                                  } else {
                                                    Dialogs.showErrorDialog(
                                                      context,
                                                      message: 'Failed to sign in with Google',
                                                    );
                                                  }
                                                }
                                              },
                                            ),
                                            if (!kIsWeb && Platform.isIOS)
                                              Padding(
                                                padding: const EdgeInsets.only(top: AppTheme.widgetPaddingSmall),
                                                child: SignInButton(
                                                  Buttons.apple,
                                                  elevation: 0,
                                                  onPressed: () async {
                                                    try {
                                                      final user = await context
                                                          .read<AuthRepository>()
                                                          .signInWithApple();
                                                      viewModel.setOrgOwner(user.uid);
                                                    } catch (e) {
                                                      if (!context.mounted) {
                                                        return;
                                                      }
                                                      if (e is UserFriendlyException) {
                                                        if (e.message != null) {
                                                          Dialogs.showErrorDialog(context, message: e.message);
                                                        }
                                                      } else {
                                                        Dialogs.showErrorDialog(
                                                          context,
                                                          message: 'Failed to sign in with Apple',
                                                        );
                                                      }
                                                    }
                                                  },
                                                ),
                                              ),
                                            if (kDebugMode)
                                              Padding(
                                                padding: const EdgeInsets.only(top: AppTheme.widgetPaddingSmall),
                                                child: SignInButton(
                                                  Buttons.email,
                                                  elevation: 0,
                                                  onPressed: () async {
                                                    final userCredential = await Dialogs.showEmailSignInDialog(context);
                                                    viewModel.setOrgOwner(userCredential?.user?.uid);
                                                  },
                                                ),
                                              ),
                                          ],
                                        )
                                      : state.orgOwner != null
                                      ? ProfileItem(
                                          profile: state.orgOwner!,
                                          onTapOverride: () => AppRouter.pushFullScreenDialog(
                                            context,
                                            ProfileDetailsScreen(profile: state.orgOwner),
                                          ),
                                        )
                                      : Center(
                                          child: CircularProgressIndicator(
                                            color: Theme.of(context).colorScheme.onPrimaryContainer,
                                          ),
                                        ),
                                ),
                              ),
                            ),
                          ContentGroup(
                            title: 'Con Name',
                            subtitle: 'Can be changed later',
                            child: SurfaceInputField(
                              child: TextField(
                                onChanged: (value) => viewModel.setName(value),
                                textInputAction: TextInputAction.next,
                                textCapitalization: TextCapitalization.words,
                                textAlign: TextAlign.center,
                                maxLength: InputConstants.maxConNameLength,
                                decoration: const InputDecoration(hintText: 'Con Name', counterText: ''),
                              ),
                            ),
                          ),
                          const SizedBox(height: AppTheme.widgetPadding),
                          ContentGroup(
                            title: 'Location',
                            subtitle: 'Can be changed later',
                            padHorizontally: false,
                            children: [
                              Padding(
                                padding: const EdgeInsets.symmetric(horizontal: AppTheme.widgetPadding),
                                child: Text(
                                  state.location?.name ?? 'Location not found',
                                  textAlign: TextAlign.center,
                                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: Theme.of(context).colorScheme.onPrimaryContainer,
                                  ),
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.symmetric(horizontal: AppTheme.widgetPadding),
                                child: Text(
                                  state.location?.address ?? '',
                                  textAlign: TextAlign.center,
                                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: Theme.of(
                                      context,
                                    ).colorScheme.onPrimaryContainer.withValues(alpha: AppTheme.subtleWidgetOpacity),
                                  ),
                                ),
                              ),
                              const SizedBox(height: AppTheme.widgetPaddingSmall),
                              Center(
                                child: OutlinedButton.icon(
                                  label: const Text('Select Location'),
                                  icon: const Icon(Icons.place),
                                  onPressed: () async {
                                    final locationModel = await AppRouter.pushFullScreenDialog<LocationModel>(
                                      context,
                                      const LocationSearchScreen(),
                                    );
                                    if (locationModel != null) {
                                      viewModel.setLocation(locationModel);
                                    }
                                  },
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: AppTheme.widgetPadding),
                          ContentGroup(
                            title: 'Date',
                            children: [
                              _buildDateTimeTile(
                                context: context,
                                label: 'Start Day',
                                errorMessage: 'Select Day',
                                value: state.firstDate != null
                                    ? DateFormat.MMMEd().format(state.firstDate!.toDateTime())
                                    : null,
                                onTap: () => _showDatePicker(
                                  context,
                                  state.firstDate,
                                  (selectedDate) => viewModel.setFirstDate(selectedDate),
                                ),
                              ),
                              _buildDateTimeTile(
                                context: context,
                                label: 'End Day',
                                errorMessage: 'Select Day',
                                value: state.lastDate != null
                                    ? DateFormat.MMMEd().format(state.lastDate!.toDateTime())
                                    : null,
                                onTap: () => _showDatePicker(
                                  context,
                                  state.lastDate,
                                  (selectedDate) => viewModel.setLastDate(selectedDate),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: AppTheme.widgetPadding),
                          _themeSection(context, viewModel, state.color, colorHexController),
                          const SizedBox(height: AppTheme.widgetPadding),
                          if (state.showTierSelector == true)
                            Padding(
                              padding: const EdgeInsets.only(bottom: AppTheme.widgetPadding),
                              child: ContentGroup(
                                title: 'Tier',
                                child: TierSelection(
                                  selectedTier: state.tier,
                                  onTierSelected: (tier) => viewModel.setTier(tier),
                                ),
                              ),
                            ),
                          ContentArea(
                            padding: EdgeInsets.zero,
                            child: GlassTileButton(
                              text: 'Submit',
                              icon: Icons.check,
                              isAccent: true,
                              onTap: viewModel.submitEnabled
                                  ? () async {
                                      final confirmDelete = await Dialogs.showConfirmationDialog(
                                        context,
                                        title: 'Submit?',
                                        message:
                                            'Submitting will create a new con but will not display to the public until payment is received',
                                      );
                                      if (!context.mounted) {
                                        return;
                                      }
                                      if (confirmDelete == true && context.mounted) {
                                        Dialogs.showLoadingDialog(context);

                                        late final ConData? createdConData;
                                        try {
                                          createdConData = await viewModel.submit();
                                        } catch (e) {
                                          createdConData = null;
                                          if (e is UserFriendlyException) {
                                            if (e.message != null && context.mounted) {
                                              Navigator.of(context, rootNavigator: true).pop();
                                              Dialogs.showErrorDialog(context, message: e.message);
                                              return;
                                            }
                                          }
                                        }

                                        if (context.mounted) {
                                          if (createdConData == null) {
                                            Navigator.of(context, rootNavigator: true).pop();
                                            await Dialogs.showErrorDialog(context, message: 'Please try again later');
                                          } else {
                                            Navigator.of(context, rootNavigator: true).pop();
                                            context.go(createdConData.conPath);
                                          }
                                        }
                                      }
                                    }
                                  : null,
                            ),
                          ),
                        ],
                      )
                    : const Center(child: CircularProgressIndicator()),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildDateTimeTile({
    required BuildContext context,
    required String label,
    required String errorMessage,
    required String? value,
    required Function() onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppTheme.borderRadius),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(AppTheme.widgetPadding),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              label,
              style: Theme.of(context).textTheme.labelMedium?.copyWith(
                color: Theme.of(context).colorScheme.onPrimaryContainer.withValues(alpha: AppTheme.subtleWidgetOpacity),
              ),
            ),
            if (value != null)
              Text(
                value,
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(color: Theme.of(context).colorScheme.onPrimaryContainer),
              ),
            if (value == null) InputErrorMessage(message: errorMessage),
          ],
        ),
      ),
    );
  }

  Future<void> _showDatePicker(BuildContext context, DateFields? initialDate, Function(DateFields) onSelected) async {
    final now = DateTime.now();
    final startDate = now.add(Duration(days: 1));
    final endDate = now.add(Duration(days: 365));

    final datePicked = await Dialogs.showDatePickerDialog(context, initialDate?.toDateTime(), startDate, endDate);
    if (datePicked != null) {
      onSelected(datePicked);
    }
  }

  Widget _themeSection(
    BuildContext context,
    CreateConViewModel viewModel,
    String? colorHex,
    TextEditingController colorHexController,
  ) {
    final demoColorHex = colorHex ?? AppTheme.defaultColorHex;
    return ContentGroup(
      title: 'Theme',
      subtitle: 'Can be changed later',
      additionalTitlePadding: true,
      padHorizontally: false,
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(AppTheme.widgetPadding, 0, AppTheme.widgetPadding, AppTheme.widgetPadding),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              DemoApp(colorHex: demoColorHex, brightness: Brightness.light),
              DemoApp(colorHex: demoColorHex, brightness: Brightness.dark),
            ],
          ),
        ),
        Wrap(
          spacing: AppTheme.widgetPadding,
          runSpacing: AppTheme.widgetPadding,
          alignment: WrapAlignment.center,
          runAlignment: WrapAlignment.center,
          crossAxisAlignment: WrapCrossAlignment.center,
          children: [
            SizedBox(
              width: 150,
              child: SurfaceInputField(
                child: TextField(
                  controller: colorHexController,
                  textAlign: TextAlign.center,
                  maxLength: 6,
                  decoration: const InputDecoration(prefixText: '#', hintText: 'FFFFFF', counterText: ''),
                  onChanged: (value) => viewModel.changeColor(value),
                ),
              ),
            ),
            OutlinedButton.icon(
              icon: const Icon(Icons.color_lens),
              label: const Text('Color Picker'),
              onPressed: () => _showColorPicker(
                context,
                hexTextController: colorHexController,
                createConViewModel: viewModel,
                colorHex: demoColorHex,
                onChanged: (hexColor) {
                  viewModel.changeColor(hexColor);
                  colorHexController.text = hexColor.substring(1);
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  void _showColorPicker(
    BuildContext context, {
    required TextEditingController hexTextController,
    required CreateConViewModel createConViewModel,
    required String colorHex,
    required void Function(String hexColor) onChanged,
  }) {
    BottomSheets.showBottomSheet(
      context: context,
      darkenBackground: false,
      child: BlocProvider.value(
        value: createConViewModel,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              width: 220,
              child: AspectRatio(
                aspectRatio: 1,
                child: BlocBuilder<CreateConViewModel, CreateConState>(
                  builder: (context, state) => ColorWheelPicker(
                    color: ColorConverter.stringToColor(colorHex),
                    onChanged: (value) => onChanged(ColorConverter.colorToString(value)),
                    onWheel: (value) {},
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
