import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:venvi/models/base_profile.dart';

part 'profile_snippet_model.freezed.dart';
part 'profile_snippet_model.g.dart';

@freezed
sealed class ProfileSnippetModel with _$ProfileSnippetModel implements BaseProfile {
  const factory ProfileSnippetModel({
    String? id,
    String? username,
    String? displayName,
    String? headline,
    String? photoUrl,
  }) = _ProfileSnippetModel;

  factory ProfileSnippetModel.fromJson(Map<String, dynamic> json) => _$ProfileSnippetModelFromJson(json);
}
