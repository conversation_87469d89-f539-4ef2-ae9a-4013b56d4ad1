import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:venvi/global_view_models/role_view_model.dart';
import 'package:venvi/models/base_profile.dart';
import 'package:venvi/models/participant_model.dart';
import 'package:venvi/router.dart';
import 'package:venvi/screen_dialogs/profile_details/profile_details_screen.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/widgets/profile_photo_view.dart';

class ProfileItem extends StatelessWidget {
  final BaseProfile? profile;
  final String? roleId;
  final IconButton? leadingButton;
  final IconButton? trailingButton;
  final bool isSearch;
  final bool showRole;
  final String? subtitleOverride;
  final void Function()? onTapOverride;

  ProfileItem({
    super.key,
    this.profile,
    this.roleId,
    this.leadingButton,
    this.trailingButton,
    this.isSearch = false,
    this.showRole = false,
    this.subtitleOverride,
    this.onTapOverride,
  }) {
    assert(!(isSearch && showRole), 'isSearch and showRole cannot both be true');
  }

  @override
  Widget build(BuildContext context) {
    bool isSpotlight = false;
    if (profile is ParticipantModel && roleId != null) {
      isSpotlight = (profile as ParticipantModel).spotlightRoles?.contains(roleId) ?? false;
    }
    final subtitleUsername = isSearch && profile?.username != null ? Text(profile!.username!) : null;
    Widget subtitleRole = const SizedBox.shrink();
    if (showRole) {
      if (roleId != null) {
        subtitleRole = Text(context.watch<RoleViewModel>().getRoleById(roleId)?.name ?? '');
      } else if (profile is ParticipantModel) {
        final roleIds = (profile as ParticipantModel).allRoles ?? [];
        final roles = roleIds.map((roleId) => context.watch<RoleViewModel>().getRoleById(roleId)?.name).toList();
        subtitleRole = Text(roles.join(', '));
      }
    }

    return ListTile(
      title: profile != null ? Text(profile?.displayName ?? 'User has no display name') : const SizedBox.shrink(),
      subtitle: subtitleOverride != null
          ? Text(subtitleOverride!)
          : isSearch
          ? subtitleUsername
          : showRole
          ? subtitleRole
          : null,
      contentPadding: Theme.of(context).listTileTheme.contentPadding?.subtract(
        EdgeInsets.only(left: leadingButton != null ? AppTheme.widgetPadding : 0, right: AppTheme.widgetPadding),
      ),
      leading: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (leadingButton != null)
            Padding(
              padding: const EdgeInsets.only(right: AppTheme.widgetPaddingVerySmall),
              child: leadingButton,
            ),
          Stack(
            alignment: Alignment.topLeft,
            children: [
              Padding(
                padding: const EdgeInsets.all(AppTheme.widgetPaddingVerySmall),
                child: ProfilePhotoView(profile: profile, size: 40),
              ),
              if (isSpotlight)
                Container(
                  padding: const EdgeInsets.all(2),
                  decoration: BoxDecoration(shape: BoxShape.circle, color: Theme.of(context).colorScheme.secondary),
                  child: Icon(Icons.star, size: 14, color: Theme.of(context).colorScheme.onSecondary),
                ),
            ],
          ),
        ],
      ),
      trailing: trailingButton ?? (isSearch ? const Icon(Icons.check) : const Icon(Icons.chevron_right)),
      onTap: () => onTapOverride != null
          ? onTapOverride!()
          : profile != null
          ? AppRouter.pushFullScreenDialog(context, ProfileDetailsScreen(profile: profile))
          : null,
    );
  }
}
