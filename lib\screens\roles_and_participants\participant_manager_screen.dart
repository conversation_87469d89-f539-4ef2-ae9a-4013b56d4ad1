import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/global_view_models/participant_view_model.dart';
import 'package:venvi/models/participant_model.dart';
import 'package:venvi/repositories/participant_repository.dart';
import 'package:venvi/router.dart';
import 'package:venvi/screen_dialogs/edit_profile/edit_manual_participant_screen.dart';
import 'package:venvi/screen_dialogs/profile_details/profile_details_screen.dart';
import 'package:venvi/screen_dialogs/profile_search/profile_search_result.dart';
import 'package:venvi/screen_dialogs/profile_search/profile_search_screen.dart';
import 'package:venvi/screens/roles_and_participants/participants_manager_state.dart';
import 'package:venvi/screens/roles_and_participants/participants_manager_view_model.dart';
import 'package:venvi/theme.dart';
import 'package:venvi/utils/dialogs.dart';
import 'package:venvi/widgets/content_area.dart';
import 'package:venvi/widgets/editor_scaffold.dart';
import 'package:venvi/widgets/glass_tile_button.dart';
import 'package:venvi/widgets/profile_item.dart';

class ParticipantManagerScreen extends StatelessWidget {
  const ParticipantManagerScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return EditorScaffold<
      ParticipantsManagerViewModel,
      ParticipantsManagerState,
      ParticipantViewModel,
      List<ParticipantModel>?
    >(
      checkForExternalConflicts: false,
      externalCubit: context.read<ParticipantViewModel>(),
      buildState: (externalCubitState) {
        final participants = externalCubitState ?? [];

        // Build original participant maps for tracking
        final originalParticipantsById = <String, ParticipantModel>{};
        final originalParticipantsByUserId = <String, ParticipantModel>{};
        final currentParticipantIds = <String>{};
        final currentUserIds = <String>{};

        for (final participant in participants) {
          if (participant.id != null) {
            originalParticipantsById[participant.id!] = participant;
            currentParticipantIds.add(participant.id!);
          }
          if (participant.userId != null) {
            originalParticipantsByUserId[participant.userId!] = participant;
            currentUserIds.add(participant.userId!);
          }
        }

        return ParticipantsManagerState(
          participants: participants,
          originalParticipantsById: originalParticipantsById,
          originalParticipantsByUserId: originalParticipantsByUserId,
          currentParticipantIds: currentParticipantIds,
          currentUserIds: currentUserIds,
        );
      },
      initViewModel: (state) =>
          ParticipantsManagerViewModel(state, context.read<ConData>(), context.read<ParticipantRepository>()),
      title: const Text('Participant Editor'),
      successMessage: 'Participants saved',
      buildContent: (context, viewModel, state) => Column(
        children: [
          Expanded(
            child: ListView.separated(
              padding: const EdgeInsets.all(AppTheme.screenPadding),
              itemCount: state.participants.length,
              separatorBuilder: (context, index) => const SizedBox(height: AppTheme.widgetPadding),
              itemBuilder: (context, index) => buildParticipantItem(context, viewModel, state.participants[index]),
            ),
          ),
          Padding(
            padding: const EdgeInsets.fromLTRB(
              AppTheme.screenPadding,
              AppTheme.widgetPaddingSmall,
              AppTheme.screenPadding,
              0,
            ),
            // padding: const EdgeInsets.all(AppTheme.screenPadding),
            child: ContentArea(
              padding: EdgeInsets.zero,
              child: GlassTileButton(
                text: 'Add Participant from Venvi Profile',
                icon: Icons.add,
                onTap: () async {
                  final List<String> excludedIds = [];
                  for (final participant in state.participants) {
                    final id = participant.id;
                    if (id != null) {
                      excludedIds.add(id);
                    }
                    final userId = participant.userId;
                    if (userId != null) {
                      excludedIds.add(userId);
                    }
                  }
                  final profileSearchResult = await AppRouter.pushFullScreenDialog<ProfileSearchResult?>(
                    context,
                    ProfileSearchScreen(
                      excludedIds: excludedIds,
                      bottomWidget: ContentArea(
                        padding: EdgeInsets.zero,
                        child: GlassTileButton(
                          text: 'Add New Participant Manually',
                          icon: Icons.add,
                          onTap: () async {
                            final manualParticipant = await AppRouter.pushFullScreenDialog<ParticipantModel?>(
                              context,
                              const EditManualParticipantScreen(),
                            );
                            if (manualParticipant != null && context.mounted) {
                              context.pop();
                              viewModel.addParticipant(manualParticipant);
                            }
                          },
                        ),
                      ),
                    ),
                  );
                  if (profileSearchResult?.participantModel != null) {
                    viewModel.addParticipant(profileSearchResult!.participantModel!);
                  } else if (profileSearchResult?.profileModel != null) {
                    viewModel.addParticipantFromProfile(profileSearchResult!.profileModel!);
                  }
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildParticipantItem(
    BuildContext context,
    ParticipantsManagerViewModel viewModel,
    ParticipantModel participant,
  ) {
    return ContentArea(
      padding: EdgeInsets.zero,
      child: ProfileItem(
        profile: participant,
        subtitleOverride: participant.userId == null ? 'Manual Entry' : participant.username ?? '',
        onTapOverride: () => AppRouter.pushFullScreenDialog(context, ProfileDetailsScreen(profile: participant)),
        trailingButton: IconButton(
          icon: const Icon(Icons.delete),
          onPressed: () async {
            final id = participant.id;
            final confirm = await Dialogs.showConfirmationDialog(
              context,
              title: 'Remove Participant?',
              message:
                  '${participant.displayName} (${participant.userId == null ? 'Manual Entry' : participant.username ?? ''}) will be removed from all events',
            );
            if (confirm == true && id != null) {
              viewModel.removeParticipant(id);
            }
          },
        ),
      ),
    );
  }
}
