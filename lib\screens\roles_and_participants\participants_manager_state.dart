import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:venvi/models/participant_model.dart';

part 'participants_manager_state.freezed.dart';

@freezed
sealed class ParticipantsManagerState with _$ParticipantsManagerState {
  const factory ParticipantsManagerState({
    required List<ParticipantModel> participants,
    required Map<String, ParticipantModel> originalParticipantsById,
    required Map<String, ParticipantModel> originalParticipantsByUserId,
    required Set<String> currentParticipantIds,
    required Set<String> currentUserIds,
  }) = _ParticipantsManagerState;
}
