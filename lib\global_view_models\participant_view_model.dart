import 'dart:async';

import 'package:venvi/constants/data_location.dart';
import 'package:venvi/constants/metadata_field.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/global_view_models/metadata_listener_view_model.dart';
import 'package:venvi/global_view_models/metadata_view_model.dart';
import 'package:venvi/models/participant_model.dart';
import 'package:venvi/repositories/participant_repository.dart';
import 'package:venvi/utils/search_util.dart';

class ParticipantViewModel extends MetadataListenerViewModel<ParticipantModel, List<ParticipantModel>?> {
  final ConData _conData;
  final ParticipantRepository _repository;

  ParticipantViewModel(this._conData, this._repository, MetadataViewModel metadataViewModel)
    : super(null, _conData, metadataViewModel, MetadataField.participants);

  @override
  Future<ParticipantModel?> getData(String id, DataLocation dataLocation) async {
    return await _repository.getParticipant(_conData, id, dataLocation: dataLocation);
  }

  @override
  void onData(Map<String, ParticipantModel> data) {
    if (isClosed) {
      return;
    }

    final sortedData = sortByDisplayName(data.values.toList());
    emit(sortedData);
  }

  List<ParticipantModel> sortByDisplayName(List<ParticipantModel> participants) {
    final sortedData = participants.toList();
    sortedData.sort(
      (a, b) => a.displayName != null
          ? b.displayName != null
                ? a.displayName!.compareTo(b.displayName!)
                : -1
          : 1,
    );
    return sortedData;
  }

  ParticipantModel? getParticipant(String id) {
    try {
      return state?.firstWhere((element) => element.id == id);
    } catch (e) {
      return null;
    }
  }

  ParticipantModel? getParticipantByUserId(String userId) {
    try {
      return state?.firstWhere((element) => element.userId == userId);
    } catch (e) {
      return null;
    }
  }

  List<ParticipantModel> searchParticipants(String searchText) {
    if (state == null) {
      return [];
    }
    return state!
        .where(
          (element) =>
              SearchUtil.searchParts(element.username, searchText) ||
              SearchUtil.searchParts(element.displayName, searchText),
        )
        .toList();
  }

  List<ParticipantModel> getParticipantsByRole(String roleId, {bool spotlightOnly = false}) {
    if (state == null) {
      return [];
    }
    return state!
        .where(
          (element) =>
              element.allRoles?.contains(roleId) == true &&
              (!spotlightOnly || element.spotlightRoles?.contains(roleId) == true),
        )
        .toList();
  }
}
