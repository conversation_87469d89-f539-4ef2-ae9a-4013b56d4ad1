import 'dart:async';

import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/models/participant_model.dart';
import 'package:venvi/models/profile_model.dart';
import 'package:venvi/repositories/participant_repository.dart';
import 'package:venvi/screens/roles_and_participants/participants_manager_state.dart';
import 'package:venvi/widgets/editor_scaffold_exception.dart';
import 'package:venvi/widgets/editor_scaffold_view_model.dart';

class ParticipantsManagerViewModel extends EditorScaffoldViewModel<ParticipantsManagerState> {
  final ConData _conData;
  final ParticipantRepository _participantRepository;

  ParticipantsManagerViewModel(super.initialState, this._conData, this._participantRepository);

  @override
  bool checkChanges(ParticipantsManagerState initialState, ParticipantsManagerState currentState) {
    return initialState.participants.length != currentState.participants.length ||
        !initialState.participants.every((element) => currentState.participants.contains(element));
  }

  @override
  Future<ParticipantsManagerState> applyChanges(
    ParticipantsManagerState initialState,
    ParticipantsManagerState state,
  ) async {
    final removedParticipants = initialState.participants
        .where((element) => !state.participants.contains(element))
        .toList();
    final addedParticipants = state.participants
        .where((element) => !initialState.participants.contains(element))
        .toList();

    final List<Future<bool>> removedFutures = [];
    final List<Future<String?>> addedFutures = [];
    for (final participant in removedParticipants) {
      if (participant.id != null) {
        removedFutures.add(_participantRepository.removeParticipant(_conData, participant.id!));
      }
    }
    for (final participant in addedParticipants) {
      if (participant.id != null) {
        addedFutures.add(_participantRepository.createParticipant(_conData, participant));
      }
    }

    final removeResults = await Future.wait(removedFutures);
    final addResults = await Future.wait(addedFutures);

    if (removeResults.contains(false) || addResults.contains(null)) {
      throw const EditorScaffoldException(['Failed to update participants']);
    }

    return state;
  }

  void addParticipantFromProfile(ProfileModel model) {
    final userId = model.id;
    if (userId == null) {
      return;
    }

    if (state.currentUserIds.contains(userId)) {
      return;
    }

    // Check if user previously existed
    final originalParticipant = state.originalParticipantsByUserId[userId];
    if (originalParticipant != null) {
      _addExistingParticipant(originalParticipant);
    } else {
      final newParticipant = ParticipantModel.fromProfileModel(model);
      _addNewParticipant(newParticipant);
    }
  }

  void addParticipant(ParticipantModel model) {
    final userId = model.userId;
    final participantId = model.id;

    // Check for duplicates
    if (userId != null && state.currentUserIds.contains(userId)) {
      return;
    }
    if (participantId != null && state.currentParticipantIds.contains(participantId)) {
      return;
    }

    // Check if user previously existed
    if (userId != null) {
      final originalParticipant = state.originalParticipantsByUserId[userId];
      if (originalParticipant != null) {
        _addExistingParticipant(originalParticipant);
        return;
      }
    }

    // Check if participant previously existed
    if (participantId != null) {
      final originalParticipant = state.originalParticipantsById[participantId];
      if (originalParticipant != null) {
        _addExistingParticipant(originalParticipant);
        return;
      }
    }

    _addNewParticipant(model);
  }

  void _addExistingParticipant(ParticipantModel participant) {
    final updatedUserIds = Set<String>.from(state.currentUserIds);
    final updatedParticipantIds = Set<String>.from(state.currentParticipantIds);

    if (participant.userId != null) {
      updatedUserIds.add(participant.userId!);
    }
    if (participant.id != null) {
      updatedParticipantIds.add(participant.id!);
    }

    emit(
      state.copyWith(
        participants: [...state.participants, participant],
        currentUserIds: updatedUserIds,
        currentParticipantIds: updatedParticipantIds,
      ),
    );
  }

  void _addNewParticipant(ParticipantModel participant) {
    final updatedUserIds = Set<String>.from(state.currentUserIds);
    final updatedParticipantIds = Set<String>.from(state.currentParticipantIds);

    if (participant.userId != null) {
      updatedUserIds.add(participant.userId!);
    }
    if (participant.id != null) {
      updatedParticipantIds.add(participant.id!);
    }

    emit(
      state.copyWith(
        participants: [...state.participants, participant],
        currentUserIds: updatedUserIds,
        currentParticipantIds: updatedParticipantIds,
      ),
    );
  }

  void removeParticipant(String participantId) {
    final removedParticipant = state.participants.where((element) => element.id == participantId).firstOrNull;
    if (removedParticipant == null) {
      return;
    }

    // Update tracking sets
    final updatedUserIds = Set<String>.from(state.currentUserIds);
    final updatedParticipantIds = Set<String>.from(state.currentParticipantIds);

    if (removedParticipant.userId != null) {
      updatedUserIds.remove(removedParticipant.userId!);
    }
    updatedParticipantIds.remove(participantId);

    emit(
      state.copyWith(
        participants: state.participants.where((element) => element.id != participantId).toList(),
        currentUserIds: updatedUserIds,
        currentParticipantIds: updatedParticipantIds,
      ),
    );
  }
}
