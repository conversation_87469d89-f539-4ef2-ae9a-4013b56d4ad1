import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:venvi/constants/data_location.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/models/participant_model.dart';
import 'package:venvi/utils/logger.dart';

class ParticipantRepository {
  final FirebaseFirestore _firestore;

  ParticipantRepository(this._firestore);

  Future<ParticipantModel?> getParticipant(ConData conData, String participantId, {DataLocation? dataLocation}) async {
    final ref = _firestore
        .collection('orgs')
        .doc(conData.orgId)
        .collection('cons')
        .doc(conData.conId)
        .collection('participants')
        .doc(participantId);
    try {
      final snapshot = await ref.get(GetOptions(source: dataLocation?.firestoreSource ?? Source.serverAndCache));

      if (snapshot.exists) {
        final data = snapshot.data();
        if (data != null) {
          return ParticipantModel.fromJson(data);
        }
      }
      return null;
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to get doc',
        documentReference: ref,
        dataLocation: dataLocation,
      );
      return null;
    }
  }

  Future<String?> createParticipant(ConData conData, ParticipantModel participantModel) async {
    final ref = _firestore
        .collection('orgs')
        .doc(conData.orgId)
        .collection('cons')
        .doc(conData.conId)
        .collection('participants');
    try {
      final docRef = await ref.add(participantModel.toJson());
      return docRef.id;
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to add doc',
        collectionReference: ref,
      );
      return null;
    }
  }

  Future<bool> updateParticipant(ConData conData, String participantId, ParticipantModel participantModel) async {
    final ref = _firestore
        .collection('orgs')
        .doc(conData.orgId)
        .collection('cons')
        .doc(conData.conId)
        .collection('participants')
        .doc(participantId);
    try {
      await ref.update(participantModel.toJson());
      return true;
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to update doc',
        documentReference: ref,
      );
      return false;
    }
  }

  Future<bool> removeParticipant(ConData conData, String participantId) async {
    final ref = _firestore
        .collection('orgs')
        .doc(conData.orgId)
        .collection('cons')
        .doc(conData.conId)
        .collection('participants')
        .doc(participantId);
    try {
      await ref.delete();
      return true;
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to delete doc',
        documentReference: ref,
      );
      return false;
    }
  }

  Future<bool> addToRole(ConData conData, String participantId, String roleId) async {
    final ref = _firestore
        .collection('orgs')
        .doc(conData.orgId)
        .collection('cons')
        .doc(conData.conId)
        .collection('participants')
        .doc(participantId);
    try {
      await ref.update({
        'allRoles': FieldValue.arrayUnion([roleId]),
      });
      return true;
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to update doc',
        documentReference: ref,
      );
      return false;
    }
  }

  Future<bool> updateRole(
    ConData conData,
    String roleId,
    List<String> allParticipantIds,
    List<String> spotlightParticipantIds,
    List<String> removedParticipantIds,
  ) async {
    final ref = _firestore
        .collection('orgs')
        .doc(conData.orgId)
        .collection('cons')
        .doc(conData.conId)
        .collection('participants');

    final batch = _firestore.batch();
    for (final id in allParticipantIds) {
      batch.update(ref.doc(id), {
        'allRoles': FieldValue.arrayUnion([roleId]),
        'spotlightRoles': spotlightParticipantIds.contains(id)
            ? FieldValue.arrayUnion([roleId])
            : FieldValue.arrayRemove([roleId]),
      });
    }
    for (final id in removedParticipantIds) {
      batch.update(ref.doc(id), {
        'allRoles': FieldValue.arrayRemove([roleId]),
        'spotlightRoles': FieldValue.arrayRemove([roleId]),
      });
    }
    try {
      await batch.commit();
      return true;
    } catch (e, stackTrace) {
      Logger.firestoreError(
        exception: e,
        stackTrace: stackTrace,
        message: 'Failed to commit batch',
        collectionReference: ref,
      );
      return false;
    }
  }

  Future<bool> updateParticipantConFields(ConData conData, String userId, String? conHeadline, String? conBio) async {
    final queryRef = _firestore
        .collection('orgs')
        .doc(conData.orgId)
        .collection('cons')
        .doc(conData.conId)
        .collection('participants')
        .where('userId', isEqualTo: userId);
    try {
      final snapshot = await queryRef.get();
      if (snapshot.docs.isEmpty) {
        return false;
      }
      for (final doc in snapshot.docs) {
        await doc.reference.update({'conHeadline': conHeadline, 'conBio': conBio});
      }
      return true;
    } catch (e, stackTrace) {
      Logger.firestoreError(exception: e, stackTrace: stackTrace, message: 'Failed to update doc', query: queryRef);
      return false;
    }
  }
}
